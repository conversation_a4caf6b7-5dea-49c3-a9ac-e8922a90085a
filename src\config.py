import yaml
import json
import os

CONFIG_PATH = os.path.join(os.path.dirname(__file__), "..", "config", "settings.yaml")
USER_MAPPING_PATH = os.path.join(os.path.dirname(__file__), "..", "config", "gesture_mappings.json")

def load_default_config():
    try:
        with open(CONFIG_PATH, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"Error loading default config: {e}")
        return {}

def load_user_mappings():
    try:
        if not os.path.exists(USER_MAPPING_PATH):
            return {}
        with open(USER_MAPPING_PATH, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading user mappings: {e}")
        return {}

def save_user_mappings(data):
    try:
        # สร้างโฟลเดอร์ถ้ายังไม่มี
        os.makedirs(os.path.dirname(USER_MAPPING_PATH), exist_ok=True)
        
        # บันทึกข้อมูล
        with open(USER_MAPPING_PATH, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
            
        print(f"Saved mappings to {USER_MAPPING_PATH}")
        print(f"Saved data: {data}")
    except Exception as e:
        print(f"Error saving user mappings: {e}")

def get_effective_mappings():
    try:
        default_config = load_default_config()
        user_mappings = load_user_mappings()

        # เริ่มจาก default แล้วอัปเดตด้วย user mapping
        mappings = default_config.get("gesture_mappings", {}).copy()
        mappings.update(user_mappings)
        return mappings
    except Exception as e:
        print(f"Error getting effective mappings: {e}")
        return {}
