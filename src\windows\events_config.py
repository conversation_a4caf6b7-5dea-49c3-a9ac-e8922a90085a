from PySide6.QtWidgets import QDialog, QVBoxLayout, QLabel, QComboBox, QPushButton, QHBoxLayout, QGroupBox
from src.config import *

class GestureConfigDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("ตั้งค่า Gesture Mapping")
        self.resize(500, 400)
        self.parent = parent  # เก็บ reference ของหน้าต่างหลัก

        layout = QVBoxLayout(self)
        self.combo_boxes = {}
        self.press_type_boxes = {}

        default_mappings = load_default_config().get("gesture_mappings", {})
        user_mappings = load_user_mappings()

        for gesture, default_key in default_mappings.items():
            group = QGroupBox(gesture)
            group_layout = QHBoxLayout()
            
            # Key selection
            key_label = QLabel("ปุ่ม:")
            combo = QComboBox()
            options = [
                "none", "w", "a", "s", "d", "q", "e", "r", "f", "z",
                "space", "left_click", "right_click", "1", "2", "3", "4", "5"
            ]
            combo.addItems(options)
            
            # Press type selection
            type_label = QLabel("รูปแบบการกด:")
            press_type = QComboBox()
            press_type.addItems(["กดครั้งเดียว", "กดค้าง"])
            
            # Set current values
            current_mapping = user_mappings.get(gesture)
            if isinstance(current_mapping, dict):
                current_val = current_mapping.get("key", default_key)
                current_type = current_mapping.get("type", "กดครั้งเดียว")
            else:
                current_val = current_mapping if current_mapping else default_key
                current_type = "กดครั้งเดียว"
            
            combo.setCurrentText(current_val if current_val in options else "none")
            press_type.setCurrentText(current_type)
            
            group_layout.addWidget(key_label)
            group_layout.addWidget(combo)
            group_layout.addWidget(type_label)
            group_layout.addWidget(press_type)
            
            group.setLayout(group_layout)
            layout.addWidget(group)
            
            self.combo_boxes[gesture] = combo
            self.press_type_boxes[gesture] = press_type

        save_btn = QPushButton("💾 บันทึก")
        save_btn.clicked.connect(self.save_mappings)
        layout.addWidget(save_btn)

    def save_mappings(self):
        # เริ่มต้นด้วยการตั้งค่าเดิม
        new_map = load_user_mappings()
        
        # อัปเดตการตั้งค่าที่เปลี่ยนแปลง
        for gesture, combo in self.combo_boxes.items():
            val = combo.currentText()
            press_type = self.press_type_boxes[gesture].currentText()
            
            if val != "none":
                new_map[gesture] = {
                    "key": val,
                    "type": press_type
                }
            elif gesture in new_map:
                # ถ้าเลือก "none" ให้ลบการตั้งค่าออก
                del new_map[gesture]

        # บันทึกการตั้งค่า
        save_user_mappings(new_map)
        
        # อัปเดตการตั้งค่าในหน้าต่างหลัก
        if self.parent:
            self.parent.update_mappings()
        
        self.accept()
