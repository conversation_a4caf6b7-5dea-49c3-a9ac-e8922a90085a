import pydirectinput
from pynput.mouse import Controller as <PERSON><PERSON><PERSON>roller, <PERSON><PERSON>

mouse = MouseController()

# แปลง string key เป็น key ที่ pydirectinput ใช้
def convert_to_key(key_str):
    key_map = {
        'space': 'space',
        'tab': 'tab',
        'ctrl': 'ctrl',
        'alt': 'alt',
        'shift': 'shift',
        'enter': 'enter',
        'backspace': 'backspace',
        'delete': 'delete',
        'up': 'up',
        'down': 'down',
        'left': 'left',
        'right': 'right'
    }
    return key_map.get(key_str, key_str)

# ปุ่มที่อาจถูกกดอยู่
possible_keys = [
    # การเคลื่อนที่พื้นฐาน
    'w', 'a', 's', 'd',
    # การกระทำพิเศษ
    'space', 'f', 'c', 'z', 'tab', '=',
    # การต่อสู้
    'q', 'e', 'b', 'r', 'x',
    # เลือกอาวุธ
    '1', '2', '3', '4', '5'
]

# กดปุ่มคีย์บอร์ด
def press_key(key):
    try:
        key_obj = convert_to_key(key)
        print(f"Pressing key: {key} (converted to: {key_obj})")  # Debug log
        pydirectinput.keyDown(key_obj)
    except Exception as e:
        print(f"Error pressing key {key}: {str(e)}")

# ปล่อยปุ่มคีย์บอร์ด
def release_key(key):
    try:
        key_obj = convert_to_key(key)
        print(f"Releasing key: {key} (converted to: {key_obj})")  # Debug log
        pydirectinput.keyUp(key_obj)
    except Exception as e:
        print(f"Error releasing key {key}: {str(e)}")

# ปล่อยปุ่มทั้งหมดที่อาจถูกกดอยู่
def release_all_keys():
    print("Releasing all keys")  # Debug log
    for key in possible_keys:
        try:
            pydirectinput.keyUp(key)
        except:
            pass

# คลิกเมาส์ซ้ายหรือขวา
def click_mouse(button="left"):
    try:
        if button == "left":
            pydirectinput.click()
        elif button == "right":
            pydirectinput.rightClick()
    except:
        pass

''''
# เลื่อนเมาส์แบบสัมพัทธ์
def move_mouse_relative(dx, dy):
    try:
        pydirectinput.moveRel(dx, dy)
    except:
        pass

# ตั้งตำแหน่งเมาส์
def set_mouse_position(x, y):
    try:
        pydirectinput.moveTo(x, y)
    except:
        pass


'''
