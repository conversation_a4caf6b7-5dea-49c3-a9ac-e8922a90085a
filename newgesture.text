motionmap1.6/
├── app.py              # ไฟล์หลักสำหรับเริ่มต้นแอพพลิเคชัน
├── config/            # โฟลเดอร์เก็บไฟล์การตั้งค่า
│   ├── gesture_mappings.json  # ไฟล์กำหนดการแมปปิ้งท่าทางกับปุ่ม
│   └── settings.yaml         # ไฟล์ตั้งค่าทั่วไป
├── src/               # โฟลเดอร์เก็บโค้ดหลัก
│   ├── body.py        # ไฟล์จัดการการตรวจจับร่างกาย
│   ├── command.py     # ไฟล์จัดการการส่งคำสั่ง
│   ├── config.py      # ไฟล์จัดการการตั้งค่า
│   ├── cv2_thread.py  # ไฟล์จัดการการทำงานของกล้อง
│   ├── movements.py   # ไฟล์จัดการการตรวจจับท่าทาง
│   ├── Pose.py        # ไฟล์กำหนดค่าจุดสำคัญบนร่างกาย
│   ├── utils/         # โฟลเดอร์เก็บฟังก์ชันเสริม
│   │   └── keyboard.py # ไฟล์จัดการการควบคุมคีย์บอร์ดและเมาส์
│   └── windows/       # โฟลเดอร์เก็บไฟล์ส่วนติดต่อผู้ใช้
│       ├── events_config.py  # ไฟล์หน้าต่างตั้งค่า
│       └── main.py    # ไฟล์หน้าต่างหลัก
└── newgesture.text    # ไฟล์คำแนะนำการเพิ่มท่าทางใหม่

PySide6 (Qt Framework)
MediaPipe
OpenCV (cv2)
PyDirectInput จำลองการกดปุ่มคีย์บอร์ดและเมาส์
PyNput เปลี่ยนในอนคตมั้ง
PyYAML อ่านไฟล์ YAML
Built-in Python Libraries
Threading(การทำงานแบบ Multi-threading สำหรับกล้อง)
math คำนวน
JSON
Time
OS จัดการ path และไฟล์ระบบ
Sys 

สรุปรายการทั้งหมด
Frameworks:

PySide6 (Qt)
External Libraries:

MediaPipe
OpenCV (cv2)
PyDirectInput
PyNput
PyYAML
Built-in Libraries:

threading
math
json
time
os
sys


ขั้นตอนเพิ่มท่าใหม่

1.สร้างฟังก์ชันตรวจจับใน src/movements.py
def ชื่อฟังก์ชัน(landmarks):
    # 1. ตรวจสอบว่า landmarks มีค่าหรือไม่
    if landmarks is None:
        return False
    
    # 2. กำหนดจุดที่ต้องการใช้
    required_points = [RIGHT_WRIST, LEFT_SHOULDER]  # ตัวอย่าง: ใช้ข้อมือขวาและไหล่ซ้าย
    if not all(p in landmarks for p in required_points):
        return False
    
    # 3. ดึงค่าพิกัดของจุดที่ต้องการ
    wrist = landmarks[RIGHT_WRIST]  # [x, y]
    shoulder = landmarks[LEFT_SHOULDER]  # [x, y]
    
    # 4. เขียนเงื่อนไขการตรวจจับ
    # ตัวอย่าง: ตรวจสอบระยะห่าง
    distance = get_distance(wrist, shoulder)
    return distance < TOUCH_THRESHOLD  # 35 พิกเซล


2.เพิ่มการตรวจจับในฟังก์ชัน detect_all_gestures()
def detect_all_gestures(landmarks, prev_landmarks):
    if landmarks is None:
        return []

    detected_gestures = []
    
    '''
    # เพิ่มเงื่อนไขใหม่
    if ชื่อฟังก์ชัน(landmarks):
        detected_gestures.append("ชื่อท่าทาง ข้อความอธิบาย")
    '''

    return detected_gestures


3.เพิ่มการตั้งค่าใน config/gesture_mappings.json
{
    "ชื่อท่าทาง ข้อความอธิบาย": {
        "key": "ปุ่มที่ต้องการ",
        "type": "กดค้าง"  // หรือ "กดครั้งเดียว"
    }
}

4.เพิ่มชื่อแสดงผลใน src/cv2_thread.py

detected_gestures = []
และ 
def get_gesture_display_name(self, gesture):
    gesture_names = {
        "ชื่อท่าทาง": "ข้อความที่ต้องการให้แสดง",
        // ... ท่าทางอื่นๆ ...
    }
    return gesture_names.get(gesture, gesture)




แผนวันนี้ 8/6/68
เพิ่มท่าใหม่ 5 ท่า 
ปัจจุบันมี3ท่า 
เอียงคอซ้าย q 
เอียงคอขวา e 
มือขวาแตะไหล่ซ้าย z หมอบ
นิ้วชี้ขวาสัมผัสสะโพกซ้าย วนเลข 1-5 ทุกครั้งที่สัมผัส 
มือขวาแตะสะโพกขวา r รีโหลด 

เพิ่มวันนี้


มือซ้ายแตะไหล่ขวา TAB เปิดกระเป๋า 
ข้อเท้าซ้าย-ขวาขยับขึ้น-ลงสลับกัน w เดินหน้า
ยกมือซ้าย a 
ยกมือขวา d
ยกมือซ้าย-ขวา ขึ้นพร้อมกัน  =  วิ่งเร็วค้าง(PUBG)



