import mediapipe as mp
import cv2

mp_pose = mp.solutions.pose
pose = mp_pose.Pose()
drawing = mp.solutions.drawing_utils

def detect_pose(frame):
    # แปลงภาพ
    image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = pose.process(image_rgb)

    if not results.pose_landmarks:
        return None, frame

    h, w, _ = frame.shape
    landmarks = {}
    for id, lm in enumerate(results.pose_landmarks.landmark):
        landmarks[id] = (int(lm.x * w), int(lm.y * h))

    # วาดโครงกระดูก
    annotated = frame.copy()
    drawing.draw_landmarks(annotated, results.pose_landmarks, mp_pose.POSE_CONNECTIONS)

    return landmarks, annotated
