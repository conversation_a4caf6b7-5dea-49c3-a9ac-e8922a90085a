from math import sqrt
from src.body import *
from src.config import *
import src.utils.keyboard as keyboard
from src.Pose import *


#x คือแนวนอน 0 (ซ้ายขวา)
#y คือแนวตั้ง 1 (บน ล่าง)

config = load_default_config()
gesture_keys = config.get("gesture_keys", {})

# ฟังก์ชันช่วยคำนวณระยะห่างระหว่างจุด
def get_distance(p1, p2):
    return sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

# ตัวแปรสำหรับนับเฟรมการเคลื่อนไหว
walk_right_counter = 0
walk_left_counter = 0
jump_counter = 0
wrist_to_knee_counter = 0


WALK_FRAME_THRESHOLD = 3  # จำนวนเฟรมขั้นต่ำที่ต้องตรวจพบการเคลื่อนไหวต่อเนื่อง
MOVE_THRESHOLD = 10     # ระยะการเคลื่อนที่ขั้นต่ำ (พิกเซล) ตั้งแยกได้เฟี้ยวพอ
TOUCH_THRESHOLD = 35     # ระยะห่างสูงสุดที่ถือว่าจุดสองจุดสัมผัสกัน

#1-5 
touching_state = False #สถานะการสัมผัส
touch_counter = 1 #ตัวนับการสัมผัส

#เอียงคอซ้าย
def Lean_left(landmarks):
    if landmarks is None:
        return False
    
    required_points = [LEFT_EAR, RIGHT_EAR]
    if not all(p in landmarks for p in required_points):
        return False
    
    ear_left = landmarks[LEFT_EAR][1]
    ear_right = landmarks[RIGHT_EAR][1]

    return (ear_left - ear_right) > 20  # หูซ้ายต่ำกว่า = เอียงคอไปซ้าย

#เอียงคอขวา
def Lean_right(landmarks):
    if landmarks is None:
        return False
    
    required_points = [RIGHT_EAR, LEFT_EAR]
    if not all(p in landmarks for p in required_points):
        return False
    
    ear_left = landmarks[LEFT_EAR][1]
    ear_right = landmarks[RIGHT_EAR][1]

    return (ear_right - ear_left) > 20  # หูซ้ายต่ำกว่า = เอียงคอไปซ้าย



# มือขวาสัมผัสไหล่ซ้าย
def right_hand_touch_left_shoulder(landmarks):
    if landmarks is None:
        return False
    
    required_points = [RIGHT_WRIST, LEFT_SHOULDER]
    if not all(p in landmarks for p in required_points):
        return False
    
    # คำนวณระยะห่างระหว่างข้อมือขวากับไหล่ซ้าย
    wrist = landmarks[RIGHT_WRIST]
    shoulder = landmarks[LEFT_SHOULDER]
    distance = get_distance(wrist, shoulder)
    
    # ถ้าระยะห่างน้อยกว่า TOUCH_THRESHOLD ถือว่าสัมผัสกัน
    return distance < TOUCH_THRESHOLD




#มือขวาแตะสะโพกขวา r รีโหลด

def right_hand_touch_right_hip(landmarks):
    if landmarks is None:
        return False
    
    required_points = [RIGHT_WRIST, RIGHT_HIP]
    if not all(p in landmarks for p in required_points):
        return False
    
    wrist = landmarks[RIGHT_WRIST]
    hip = landmarks[RIGHT_HIP]
    distance = get_distance(wrist, hip)
    
    return distance < TOUCH_THRESHOLD

#1-5 นิ้วชี้ขวาสัมผัสสะโพกซ้าย
def right_index_touch_left_hip_counter(landmarks):
    global touching_state, touch_counter

    if landmarks is None:
        return None
    
    if RIGHT_INDEX not in landmarks or LEFT_HIP not in landmarks:
        return None
    
    index = landmarks[RIGHT_INDEX]
    hip = landmarks[LEFT_HIP]
    distance = get_distance(index, hip)

    if distance < TOUCH_THRESHOLD:
        if not touching_state:
            # เพิ่งเริ่มสัมผัส
            touching_state = True
            current_number = touch_counter
            touch_counter += 1
            if touch_counter > 5:
                touch_counter = 1
            return f"count_{current_number}"
        else:
            # กำลังสัมผัสอยู่ (รอบเดิม)
            return None
    else:
        touching_state = False
        return None



def detect_all_gestures(landmarks, prev_landmarks):
    if landmarks is None:
        return []

    detected_gestures = []
    
    if Lean_left(landmarks):
        detected_gestures.append("lean_left เอียงซ้าย")
    
    if Lean_right(landmarks):
        detected_gestures.append("lean_right เอียงขวา")

    if right_hand_touch_left_shoulder(landmarks):
        detected_gestures.append("right_hand_touch_left_shoulder มือขวาสัมผัสไหล่ซ้าย")

    if right_hand_touch_right_hip(landmarks):
        detected_gestures.append("right_hand_touch_right_hip มือขวาสัมผัสสะโพกขวา") #ขวา
        
    if right_index_touch_left_hip_counter(landmarks):
        detected_gestures.append("right_index_touch_left_hip_counter นิ้วชี้ขวาสัมผัสสะโพกซ้าย ({current_number})") #ซ้าย
    
    return detected_gestures




''' เขียนรวมมันไม่เท่ส์ 
def get_head_tilt(landmarks, threshold=5):
    if landmarks is None:
        return "neutral"

    required_points = [LEFT_EAR, RIGHT_EAR]
    if not all(p in landmarks for p in required_points):
        return "neutral"

    y_left = landmarks[LEFT_EAR][1]
    y_right = landmarks[RIGHT_EAR][1]

    diff = y_left - y_right

    if diff > threshold:
        return "left"
    elif diff < -threshold:
        return "right"
    else:
        return "neutral"

'''