import cv2
import threading
import time
from PySide6.QtCore import QObject, Signal, QMutex
from PySide6.QtGui import QImage
from src.body import *
from src import movements
from src.movements import *
from src.config import *
from src.command import CommandHandler

class CameraThread(QObject):
    update_frame = Signal(QImage)
    update_gestures = Signal(list)  # New signal for gesture updates
    update_status = Signal(str)
    update_debug = Signal(str)  # เพิ่ม signal สำหรับส่งข้อมูล debug

    def __init__(self, cam_index=0):
        super().__init__()
        self.cam_index = cam_index
        self.running = False
        self.prev_landmarks = {}
        self.fps = 0
        self.thread = None
        self.mutex = QMutex()
        self.active_gestures = []  # Track active gestures
        self.debug_info = ""  # เก็บข้อมูล debug
        self.command_handler = CommandHandler()

    def start(self):
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self.run)
            self.thread.start()

    def run(self):
        cap = cv2.VideoCapture(self.cam_index)
        prev_time = time.time()
        
        while self.running:
            ret, frame = cap.read()
            if not ret:
                continue

            # Process frame and get landmarks
            landmarks, annotated = detect_pose(frame)
            
            # Get all detected gestures
            detected_gestures = []
            if landmarks is not None:
                # Check each gesture type
              
                if Lean_left(landmarks):
                    detected_gestures.append("เอียงคอซ้าย")
                if Lean_right(landmarks):
                    detected_gestures.append("เอียงคอขวา")
               
                if right_hand_touch_left_shoulder(landmarks):
                    detected_gestures.append("มือขวาสัมผัสไหล่ซ้าย") 

                if right_hand_touch_right_hip(landmarks):   
                    detected_gestures.append("มือขวาสัมผัสสะโพกขวา")

                if right_index_touch_left_hip_counter(landmarks):
                    detected_gestures.append("right_index_touch_left_hip_counter นิ้วชี้ขวาสัมผัสสะโพกซ้าย ({current_number})")

            # Update active gestures display
            self.active_gestures = []
            for gesture in detected_gestures:
                mappings = load_user_mappings()
                mapping = mappings.get(gesture)
                if mapping:
                    if isinstance(mapping, dict):
                        key = mapping.get("key", "none")
                        press_type = mapping.get("type", "กดครั้งเดียว")
                    else:
                        key = mapping
                        press_type = "กดครั้งเดียว"
                    
                    if key != "none":
                        gesture_display_name = self.get_gesture_display_name(gesture)
                        key_display_name = self.get_key_display_name(key)
                        new_gesture = (gesture_display_name, f"{key_display_name} ({press_type})")
                        if new_gesture not in self.active_gestures:
                            self.active_gestures.append(new_gesture)
                            if len(self.active_gestures) > 5:
                                self.active_gestures.pop(0)

            # Handle gestures with command handler
            self.command_handler.handle_gestures(detected_gestures)
            
            # Update UI
            self.update_gestures.emit(self.active_gestures)
            
            # Update debug info
            debug_text = []
            if detected_gestures:
                debug_text.append(f"ตรวจพบท่าทาง: {', '.join(detected_gestures)}")
            else:
                debug_text.append("กำลังรอท่าทาง...")
            
            self.debug_info = "\n".join(debug_text)
            self.update_debug.emit(self.debug_info)

            # Draw debug info on frame
            y_offset = 30
            for i, line in enumerate(debug_text):
                cv2.putText(annotated, line, (10, y_offset + i*30),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # Convert to QImage and emit
            annotated = cv2.flip(annotated, 1)
            rgb_image = cv2.cvtColor(annotated, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
            self.update_frame.emit(qt_image)

            # Calculate FPS
            now = time.time()
            self.fps = 1 / (now - prev_time)
            prev_time = now

        cap.release()
        self.command_handler.cleanup()

    def stop(self):
        self.running = False
        if self.thread:
            self.thread.join()
        self.command_handler.cleanup()
            
    def get_gesture_display_name(self, gesture):
        """แปลงชื่อท่าทางเป็นข้อความที่อ่านง่าย"""
        gesture_names = {
            "lean_left": "เอียงคอซ้าย",
            "lean_right": "เอียงคอขวา",
            "right_hand_touch_left_shoulder": "มือขวาสัมผัสไหล่ซ้าย",
            "right_hand_touch_right_hip": "มือขวาสัมผัสสะโพกขวา",
            "right_index_touch_left_hip_counter": "นิ้วชี้ขวาสัมผัสสะโพกซ้าย ({current_number})"
        }
        return gesture_names.get(gesture, gesture)
        
    def get_key_display_name(self, key):
        #แปลงชื่อปุ่มเป็นข้อความที่อ่านง่าย
        key_names = {
            "left_click": "คลิกซ้าย",
            "right_click": "คลิกขวา",
            "space": "Space",
        }
        return key_names.get(key, key.upper())


