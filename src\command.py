import time
from src.utils.keyboard import press_key, release_key, release_all_keys, click_mouse
from src.config import load_user_mappings
import json

class CommandHandler:
    def __init__(self):
        self.active_gestures = set()  # Changed from current_gesture to active_gestures set
        self.mappings = load_user_mappings()
        self.is_holding = {}  # Dictionary to track holding state for each gesture
        self.current_keys = {}  # Dictionary to track current key for each gesture

    def handle_gestures(self, gestures):
        # Convert gestures to set for easier comparison
        new_gestures = set(gestures) if gestures else set()
        
        #print(f"Detected gestures: {new_gestures}")   Debug log
        
        # Find gestures that are no longer active
        removed_gestures = self.active_gestures - new_gestures
        
        # Release keys for removed gestures
        for gesture in removed_gestures:
            print(f"Releasing gesture: {gesture}")  # Debug log
            self._release_gesture(gesture)
            self.active_gestures.remove(gesture)
        
        # Handle new gestures
        for gesture in new_gestures:
            if gesture not in self.active_gestures:
                print(f"Pressing gesture: {gesture}")  # Debug log
                self._press_gesture(gesture)
                self.active_gestures.add(gesture)
            else:
                # Handle holding state for existing gestures
                mapping = self.mappings.get(gesture)
                if mapping and isinstance(mapping, dict) and mapping.get("type") == "กดค้าง":
                    if not self.is_holding.get(gesture) or self.current_keys.get(gesture) != mapping.get("key"):
                        print(f"Re-pressing held gesture: {gesture}")  # Debug log
                        self._press_gesture(gesture)

    def _press_gesture(self, gesture):
        mapping = self.mappings.get(gesture)
        if not mapping:
            print(f"No mapping found for gesture: {gesture}")  # Debug log
            return

        # ตรวจสอบรูปแบบการตั้งค่า
        if isinstance(mapping, dict):
            key = mapping.get("key")
            press_type = mapping.get("type", "กดครั้งเดียว")
        else:
            key = mapping
            press_type = "กดครั้งเดียว"

        print(f"Pressing key: {key} with type: {press_type}")  # Debug log

        if not key or key == "none":
            print(f"Invalid key for gesture: {gesture}")  # Debug log
            return

        if key == "left_click":
            click_mouse("left")
        elif key == "right_click":
            click_mouse("right")
        else:
            if press_type == "กดค้าง":
                # กดค้างไว้
                press_key(key)
                self.is_holding[gesture] = True
                self.current_keys[gesture] = key
            else:  # กดครั้งเดียว
                # กดและปล่อยทันที
                press_key(key)
                release_key(key)
                self.is_holding[gesture] = False
                self.current_keys[gesture] = None

    def _release_gesture(self, gesture):
        mapping = self.mappings.get(gesture)
        if not mapping:
            return

        # ตรวจสอบรูปแบบการตั้งค่า
        if isinstance(mapping, dict):
            key = mapping.get("key")
            press_type = mapping.get("type", "กดครั้งเดียว")
        else:
            key = mapping
            press_type = "กดครั้งเดียว"

        if press_type == "กดค้าง" and key not in ["left_click", "right_click"]:
            release_key(key)
            self.is_holding[gesture] = False
            self.current_keys[gesture] = None

    def cleanup(self):
        # Release all active keys
        for gesture in list(self.active_gestures):
            self._release_gesture(gesture)
        self.active_gestures.clear()
        self.is_holding.clear()
        self.current_keys.clear()

def execute_gesture_action(gesture_name):
    try:
        # โหลดการตั้งค่าจาก JSON
        mappings = load_user_mappings()
        
        if gesture_name in mappings:
            key = mappings[gesture_name]
            print(f"Executing gesture: {gesture_name} -> Key: {key}")  # Debug print
            press_key(key)
            
            if gesture_name not in ["walk_right", "walk_left"]:  # ปล่อยปุ่มหลังจากกดสักครู่ (ยกเว้นปุ่มเดิน)
                time.sleep(0.1)
                release_key(key)
            return True
        else:
            print(f"Gesture not mapped: {gesture_name}")  # Debug print
            return False
    except Exception as e:
        print(f"Error executing gesture {gesture_name}: {str(e)}")  # Debug print
        return False


