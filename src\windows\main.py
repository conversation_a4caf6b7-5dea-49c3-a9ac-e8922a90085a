import sys
from PySide6.QtWidgets import (
    Q<PERSON><PERSON><PERSON>, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout,
    QLabel, QWidget, QFrame, QScrollArea
)
from PySide6.QtGui import <PERSON>Font, QPixmap, QImage
from PySide6.QtCore import Qt, Slot, QTimer
from src.cv2_thread import CameraThread
from src.config import *
from src.windows.events_config import *

class GestureDisplayPanel(QFrame):
    def __init__(self):
        super().__init__()
        self.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
            QLabel {
                color: #ecf0f1;
                font-size: 14px;
                padding: 2px;
            }
        """)
        
        self.layout = QVBoxLayout(self)
        self.title = QLabel("🎮 GESTURE DETECTED")
        self.title.setStyleSheet("font-size: 16px; font-weight: bold; color: #3498db;")
        self.title.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.title)
        
        self.separator_top = QLabel("-------------------------")
        self.separator_top.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.separator_top)
        
        self.gesture_layout = QVBoxLayout()
        self.layout.addLayout(self.gesture_layout)
        
        self.separator_bottom = QLabel("-------------------------")
        self.separator_bottom.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.separator_bottom)

    def update_gestures(self, gesture_list):
        # Clear existing gestures
        for i in reversed(range(self.gesture_layout.count())): 
            self.gesture_layout.itemAt(i).widget().setParent(None)
        
        # Add new gestures
        for i, (gesture, key) in enumerate(gesture_list, 1):
            gesture_label = QLabel(f"{i}. {gesture} → กดปุ่ม: {key}")
            gesture_label.setAlignment(Qt.AlignLeft)
            self.gesture_layout.addWidget(gesture_label)

class DebugPanel(QFrame):
    def __init__(self):
        super().__init__()
        self.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
            QLabel {
                color: #2ecc71;
                font-family: 'Consolas', monospace;
                font-size: 12px;
                padding: 2px;
            }
        """)
        
        self.layout = QVBoxLayout(self)
        self.title = QLabel("🔍 DEBUG INFO")
        self.title.setStyleSheet("font-size: 14px; font-weight: bold; color: #e74c3c;")
        self.title.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.title)
        
        self.debug_text = QLabel("")
        self.debug_text.setAlignment(Qt.AlignLeft)
        self.debug_text.setWordWrap(True)
        self.layout.addWidget(self.debug_text)

    def update_debug_info(self, text):
        self.debug_text.setText(text)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("MotionMap - กล้องควบคุมท่าทาง")
        self.setMinimumSize(1000, 600)  # เพิ่มความกว้างหน้าต่าง

        # สีพาสเทลสบายตาแบบเอวฟ้า
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f4f8;
                color: #333;
                font-family: 'Segoe UI';
                font-size: 16px;
            }
            QPushButton {
                background-color: #ffffff;
                color: #333;
                padding: 10px 20px;
                border: 1px solid #ccc;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #d0ebff;
            }
            QPushButton:checked {
                background-color: #74c0fc;
                color: white;
            }
        """)

        # สร้าง Main Widget และ Layout
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        self.setCentralWidget(main_widget)

        # Left Panel (Camera + Controls)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # HEADER
        title = QLabel("MotionMap Camera")
        title.setFont(QFont("Segoe UI", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(title)

        # PREVIEW FRAME
        self.preview_label = QLabel()
        self.preview_label.setFixedSize(640, 360)
        self.preview_label.setStyleSheet("background-color: #dbe4f0; border: 2px dashed #ccc;")
        self.preview_label.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(self.preview_label, alignment=Qt.AlignCenter)

        # BUTTONS
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("⏺ Start")
        self.start_btn.setCheckable(True)
        self.start_btn.clicked.connect(self.toggle_camera)

        self.stop_btn = QPushButton("⏹ Stop")
        self.stop_btn.clicked.connect(self.force_stop_camera)

        self.config_btn = QPushButton("⚙️ Settings")
        self.config_btn.clicked.connect(self.open_config_user)

        button_layout.addStretch()
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.config_btn)
        button_layout.addStretch()
        left_layout.addLayout(button_layout)

        # STATUS
        self.status_label = QLabel("Status: ● OFFLINE")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #910; font-weight: bold;")
        left_layout.addWidget(self.status_label)

        main_layout.addWidget(left_panel, stretch=2)

        # (Gesture + Debug)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Gesture Panel
        self.gesture_panel = GestureDisplayPanel()
        right_layout.addWidget(self.gesture_panel)
        
        # Debug Panel
        self.debug_panel = DebugPanel()
        right_layout.addWidget(self.debug_panel)
        
        main_layout.addWidget(right_panel, stretch=1)

        # CAMERA THREAD
        self.camera_thread = CameraThread()
        self.camera_thread.update_frame.connect(self.update_image)
        self.camera_thread.update_gestures.connect(self.update_gesture_display)
        self.camera_thread.update_debug.connect(self.update_debug_display)

        # Timer for status updates
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_label)

    def update_gesture_display(self, gesture_list):
        self.gesture_panel.update_gestures(gesture_list)

    def toggle_camera(self):
        if self.start_btn.isChecked():
            self.start_btn.setText("🔴 Live")
            self.camera_thread.start()
            self.status_timer.start(200)
        else:
            self.start_btn.setText("⏺ Start")
            self.camera_thread.stop()
            self.status_timer.stop()
            self.status_label.setText("Status: ● OFFLINE")

    def update_status_label(self):
        if self.camera_thread.running:
            fps = self.camera_thread.fps
            self.status_label.setText(f"Status: ● LIVE    FPS: {fps:.1f}")
        else:
            self.status_label.setText("Status: ● OFFLINE")

    def force_stop_camera(self):
        self.start_btn.setChecked(False)
        self.toggle_camera()

    def open_config_user(self):
        dlg = GestureConfigDialog(self)
        dlg.exec()

    def update_mappings(self):
        # อัปเดตการตั้งค่าในกล้อง
        if self.camera_thread:
            self.camera_thread.command_handler.mappings = load_user_mappings()

    @Slot(QImage)
    def update_image(self, image: QImage):
        self.preview_label.setPixmap(QPixmap.fromImage(image))

    def update_debug_display(self, debug_text):
        self.debug_panel.update_debug_info(debug_text)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
